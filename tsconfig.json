{"compilerOptions": {"target": "esnext", "jsx": "preserve", "lib": ["esnext", "dom", "dom.iterable", "scripthost"], "experimentalDecorators": true, "baseUrl": ".", "module": "esnext", "moduleResolution": "<PERSON><PERSON><PERSON>", "paths": {"@/*": ["src/*"], "~root/*": ["./*"]}, "types": ["node", "unplugin-vue-router/client", "vite-plugin-pwa/client", "@intlify/unplugin-vue-i18n/messages"], "allowJs": true, "strictNullChecks": false, "noImplicitAny": false, "noUnusedLocals": true, "noUnusedParameters": true, "importHelpers": true, "sourceMap": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "verbatimModuleSyntax": true, "skipLibCheck": true}, "include": ["src/App.vue", "src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "src/types/components.d.ts", "src/types/auto-imports.d.ts", "src/types/typed-router.d.ts"]}