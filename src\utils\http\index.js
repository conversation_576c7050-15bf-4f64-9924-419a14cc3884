import FuniJS from '@funi-lib/utils';
import { ElNotification, ElMessageBox } from 'element-plus';

let abortController = typeof AbortController !== 'undefined' ? new AbortController() : null;

const getServerBaseApi = () => {
  const pathComponents = window.location.pathname
    .replace(/\/site\/.*/, '/siteapp/')
    .replace(/\/h5\/.*/, '/h5app/')
    .replace(/\/mir\/.*/, '/mirapp/')
    .split('/')
    .filter(i => !!i);

  const path =
    pathComponents.length > 1
      ? pathComponents.slice(0, -1).join('/')
      : pathComponents.filter(i => !i.includes('app'))[0];

  if (!FuniJS.isProduction()) return ['/api', path].filter(Boolean).join('/');

  if (!!window.getBaseURL) return window.getBaseURL();

  const origin = window.location.origin;
  return [origin, path].filter(Boolean).join('/');
};

export class BaseApi extends FuniJS.Http {
  constructor() {
    super({
      baseURL: getServerBaseApi(),
      timeout: 60000
    });
  }

  static getInstance() {
    if (!BaseApi._instance) {
      BaseApi._instance = new BaseApi();
    }
    return BaseApi._instance;
  }

  //////////////////////////////////////////////////////////////////////////////////////////

  pathToRegExp(pattern) {
    const escaped = pattern
      .replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
      .replace(/\\\*\\\*/g, '.*')
      .replace(/\\\*/g, '[^/]*');
    return new RegExp(`^${escaped}$`);
  }

  getPathname(url) {
    // 移除hash部分 (#及其后面的内容)
    url = url.split('#')[0];

    // 如果是完整的URL（以http或https开头）
    if (/^https?:\/\//.test(url)) {
      // 移除协议、域名、查询参数和hash，只保留路径部分
      return url.replace(/^https?:\/\/[^/]+/, '').split('?')[0];
    }

    // 如果是相对路径
    // 确保url以/开头
    const normalizedUrl = url.startsWith('/') ? url : `/${url}`;
    // 移除查询参数
    return normalizedUrl.split('?')[0];
  }

  getQueryParams(url) {
    const params = url.split('?')[1] || '';
    return params
      .split('&')
      .filter(item => !!item)
      .reduce((pre, cur) => {
        const [key, value] = cur.split('=');
        return !!key
          ? {
              ...pre,
              [key]: value
            }
          : pre;
      }, {});
  }

  toQueryString(params) {
    if (!params || !Object.keys(params).length) return '';
    return Object.entries(params)
      .map(([key, value]) => `${key}=${value}`)
      .join('&');
  }

  async queryEncryptList() {
    try {
      if (!!this.encryptList) return Promise.resolve(this.encryptList);
      if (!this.queryEncryptListPromise) {
        this.queryEncryptListPromise = super.fetch('/gateway/resource/acl/queryEncryptList');
      }
      const res = await this.queryEncryptListPromise;
      this.encryptList = FuniJS.CryptoUtil.gatewayDecrypt(res) || [];
      console.debug('this.encryptList', this.encryptList);
    } catch (error) {
      this.encryptList = [];
    }
  }

  isNeedEncrypt(url) {
    if (!this.encryptList || !this.encryptList.length || !url || typeof url !== 'string') return false;
    const path = this.getPathname(url);
    return this.encryptList.some(i => this.pathToRegExp(i).test(path));
  }

  //axiox拦截处理begin

  //实现request拦截
  interceptorsRequest(config) {
    config.signal = config?.signal || abortController.signal;
    const token = sessionStorage.getItem('token');
    const requestId = FuniJS.guid();
    config.headers['X-FuniPaas-Request-Id'] = requestId;
    config.headers['Content-Type'] = config.headers['Content-Type'] || 'application/json;charset=utf-8';
    if (!!token) {
      config.headers['X-FuniPaas-Authorization'] = token;
      const pathname = this.getPathname(config.url);
      config.headers['X-FuniPaas-Request-Hash'] = FuniJS.SM.sm3([pathname, requestId, token].join('$$'));
    }
    return config;
  }

  //实现response拦截
  interceptorsResponse(response) {
    if (response instanceof Blob) {
      return { data: response };
    } else if (typeof response === 'string') {
      try {
        return { data: FuniJS.CryptoUtil.isadminDecrypt(response) };
      } catch (error) {
        return { data: response };
      }
    } else if (![0, 200].includes(response.status) && response.success !== true) {
      return this.handleError(response);
    }
    if (!!response.dataEncrypt && FuniJS.isString(response.data)) {
      const decryptdData = FuniJS.CryptoUtil.decryptdData(response.data);
      response.data = decryptdData ? JSON.parse(decryptdData) || {} : response.data;
    }
    return response;
  }

  handleError(response) {
    const respect = super.handleError(response);
    respect.catch(err => {
      if (['100001', '100002', '100003', '100004', '990001', '990002'].includes(err.code)) {
        abortController.abort();
        /* TODO 退出登录 并重定向登录页 临时处理为前端重定向  */
        ElMessageBox.confirm(err.message, err.phrase, {
          confirmButtonText: '知道了',
          type: 'warning',
          showClose: false,
          showCancelButton: false,
          closeOnClickModal: false,
          closeOnPressEscape: false
        }).then(() => {
          abortController = new AbortController();
          sessionStorage.clear();
          window.location.replace($utils.getLoginLocation());
        });
      } else if ([404].includes(err.status)) {
        console.error(err);
      } else if ([502, 503].includes(err.status)) {
        let ops = {
          title: `Error(${err.status})`,
          message: err.message,
          type: 'error'
        };
        ElNotification(ops);
      } else {
        let ops = {
          title: err.phrase && err.code ? `${err.phrase}(${err.code})` : err.phrase || err.message,
          type: 'error'
        };
        if (err.phrase) ops.message = err.message;
        ElNotification(ops);
      }
    });

    return respect;
  }

  async post(url, param, config = {}) {
    await this.queryEncryptList();
    if (this.isNeedEncrypt(url)) {
      const postData = FuniJS.isFormData(param) ? param : FuniJS.CryptoUtil.gatewayEncrypt(param || {});
      const contentType = FuniJS.isFormData(param) ? 'multipart/form-data' : 'text/plain';
      const postConfig = Object.assign({}, config, {
        headers: Object.assign({}, config.headers, { 'Content-Type': contentType })
      });
      return super
        .post(url, postData, postConfig)
        .then(data => (FuniJS.isString(data) ? FuniJS.CryptoUtil.gatewayDecrypt(data) : data));
    }

    return super.post(url, param, config);
  }

  async fetch(url, param, headers = {}, config = {}) {
    await this.queryEncryptList();
    if (this.isNeedEncrypt(url)) {
      const searchParams = new URLSearchParams(url.split('?')[1]);
      !!param && Object.entries(param).forEach(([key, value]) => searchParams.set(key, value));
      const paramString = searchParams.toString();
      return super
        .fetch(
          url.split('?')[0],
          !!paramString ? { data: FuniJS.CryptoUtil.gatewayEncrypt(paramString) } : {},
          headers,
          config
        )
        .then(data => (FuniJS.isString(data) ? FuniJS.CryptoUtil.gatewayDecrypt(data) : data));
    }
    return super.fetch(url, param, headers, config);
  }

  downloadFile(url, param, config = {}) {
    if (this.isNeedEncrypt(url)) {
      const postData = FuniJS.isFormData(param) ? param : FuniJS.CryptoUtil.gatewayEncrypt(param || {});
      const contentType = FuniJS.isFormData(param) ? 'multipart/form-data' : 'text/plain';
      const postConfig = Object.assign({}, config, {
        headers: Object.assign({}, config.headers, { 'Content-Type': contentType })
      });
      return super.downloadFile(url, postData, postConfig);
    }
    return super.downloadFile(url, param, config);
  }

  /**
   * @description: 文件上传
   * @param {*} url      接口地址
   * @param {*} formData 文件数据
   * @param {*} config   自定义config
   * @return {*}
   */
  upload2(url, formData, configer = {}) {
    return new Promise((resolve, reject) => {
      const config = {
        headers: { 'Content-Type': 'multipart/form-data' },
        responseType: 'blob',
        ...configer
      };
      this._axios.post(url, formData, config).then(response => {
        const contentType = response && response.headers ? response.headers['content-type'] : '';
        if (contentType.toLowerCase().includes('application/octet-stream')) {
          this.downloadDataHandler(response)
            .then(() => resolve(response))
            .catch(err => reject(err));
        } else if (response && response.data) {
          var reader = new FileReader();
          reader.onload = function () {
            var dataUrl = reader.result;
            var base64 = dataUrl.split(',')[1]; // 将 dataUrl 转换为 base64 编码的字符串
            var decodedData = atob(base64); // 解码 base64
            let realResponse = {};
            try {
              realResponse = JSON.parse(decodedData);
            } catch (ex) {
              console.log(ex);
            }
            resolve(realResponse);
          };
          reader.readAsDataURL(response?.data);
        } else {
          resolve(response);
        }
      }, reject);
    });
  }
  //axiox拦截处理end

  /////////////////////////////////////////////////////////////////////////////////
}

export default BaseApi.getInstance();
